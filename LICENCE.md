MIT License

Copyright (c) 2024 iErcann - (https://github.com/iErcann/Notblox)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


**Restriction on Use in Blockchain and Cryptocurrency Technologies**

This project is licensed under the MIT License, with the following additional restriction:

The software may **not** be used for any purpose related to blockchain, cryptocurrency, or distributed ledger technologies. This includes, but is not limited to:
   - Creating, mining, trading, or promoting cryptocurrencies.
   - Using in any application that facilitates cryptocurrency transactions.
   - Employing the software in mining, staking, or token-related activities.

Any use of this project in the cryptocurrency or blockchain industry is strictly prohibited without prior written consent from the author.
