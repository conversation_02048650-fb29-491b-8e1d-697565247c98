{"name": "NotBlox", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"next dev -p 4000\" \"serve -s public/assets -l 4001 --cors\"", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@react-three/drei": "^10.7.5", "@react-three/fiber": "^9.3.0", "@types/dompurify": "^3.2.0", "camera-controls": "^2.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.4", "lucide-react": "^0.473.0", "msgpackr": "^1.9.9", "next": "^15.2.3", "pako": "^2.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-joystick-component": "^6.2.1", "react-markdown": "^9.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.172.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "latest", "@types/pako": "^2.0.3", "@types/react": "latest", "@types/react-dom": "latest", "@types/three": "^0.172.0", "autoprefixer": "latest", "concurrently": "^8.2.2", "eslint": "latest", "eslint-config-next": "^15.1.5", "postcss": "latest", "serve": "^14.2.4", "tailwindcss": "latest", "typescript": "latest"}}