// app/ocean-demo/OceanDemoClient.tsx
'use client'

import dynamic from 'next/dynamic'

// Import dynamique pour éviter les erreurs SSR avec Three.js
const OceanDemo = dynamic(() => import('@/components/Ocean/OceanDemo'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-screen bg-black">
      <div className="text-white text-xl">Chargement de la démo océan...</div>
    </div>
  )
})

export default function OceanDemoClient() {
  return <OceanDemo />
}
