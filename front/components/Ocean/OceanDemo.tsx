// OceanDemo.tsx - Exemple d'utilisation de l'océan avec React Three Fiber
'use client'

import { Canvas } from '@react-three/fiber'
import { OrbitControls, Stats } from '@react-three/drei'
import { Suspense } from 'react'
import Ocean from './Ocean'

function Scene() {
  return (
    <>
      {/* Lumière ambiante */}
      <ambientLight intensity={0.5} />
      
      {/* Lumière directionnelle pour les ombres */}
      <directionalLight
        position={[100, 100, -250]}
        intensity={2}
        color={0xff8a0d}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={500}
        shadow-camera-left={-150}
        shadow-camera-right={150}
        shadow-camera-top={150}
        shadow-camera-bottom={-150}
      />
      
      {/* Océan */}
      <Ocean />
      
      {/* Cube flottant pour référence */}
      <mesh position={[0, 5, 0]} castShadow>
        <boxGeometry args={[2, 2, 2]} />
        <meshStandardMaterial color="orange" />
      </mesh>
      
      {/* Plan invisible pour les ombres */}
      <mesh position={[0, -15, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[1000, 1000]} />
        <shadowMaterial opacity={0.3} />
      </mesh>
      
      {/* Contrôles de caméra */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        target={[0, 0, 0]}
        maxPolarAngle={Math.PI / 2}
      />
    </>
  )
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-white text-xl">Chargement de l'océan...</div>
    </div>
  )
}

export default function OceanDemo() {
  return (
    <div className="w-full h-screen bg-black">
      <Canvas
        shadows
        camera={{ position: [50, 30, 50], fov: 75 }}
        gl={{ 
          antialias: true, 
          stencil: false, 
          powerPreference: 'high-performance',
          shadowMap: true
        }}
      >
        <Suspense fallback={null}>
          <Scene />
        </Suspense>
        <Stats />
      </Canvas>
      
      {/* Interface utilisateur */}
      <div className="absolute top-4 left-4 text-white bg-black bg-opacity-50 p-4 rounded">
        <h2 className="text-xl font-bold mb-2">Démo Océan</h2>
        <p className="text-sm">
          • Utilisez la souris pour naviguer<br/>
          • Molette pour zoomer<br/>
          • L'océan est animé en temps réel
        </p>
      </div>
    </div>
  )
}
