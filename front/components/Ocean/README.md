# Intégration de l'Océan dans NotBlox

Ce module intègre un système d'océan réaliste dans le moteur de jeu NotBlox, basé sur Three.js et utilisant les shaders Water et Sky.

## Fichiers

### `Ocean.tsx`
Composant React Three Fiber pour utilisation dans des scènes R3F standard.

### `Ocean.ts`
Classe TypeScript pure pour intégration directe dans le moteur de rendu du jeu.

### `OceanDemo.tsx`
Composant de démonstration montrant l'océan avec React Three Fiber.

## Fonctionnalités

- **Eau réaliste** : Utilise le shader Water de Three.js avec des normales procédurales
- **Ciel dynamique** : Intégration du shader Sky avec positionnement du soleil
- **Animation temps réel** : Vagues animées en continu
- **Performance optimisée** : Gestion mémoire et nettoyage automatique
- **Fallback texture** : Génération procédurale si la texture n'est pas disponible

## Intégration dans le jeu

L'océan est automatiquement ajouté au moteur de rendu principal dans `Renderer.ts` :

```typescript
// L'océan est créé automatiquement lors de l'initialisation du renderer
private addOcean() {
  console.log('Adding ocean to the scene...')
  this.ocean = new Ocean(this.scene, this)
}
```

## Configuration

### Position de l'eau
- **Y = -13** : Position verticale de la surface de l'eau
- **Taille = 10000x10000** : Dimensions du plan d'eau

### Paramètres du ciel
- **Turbidité = 10** : Clarté atmosphérique
- **Rayleigh = 2** : Diffusion de Rayleigh
- **Élévation = 2°** : Hauteur du soleil
- **Azimut = 180°** : Direction du soleil

### Couleurs
- **Eau = 0x001e0f** : Vert foncé océanique
- **Soleil = 0xffffff** : Blanc pur

## Utilisation

### Dans le moteur de jeu
L'océan est automatiquement intégré. Aucune configuration supplémentaire requise.

### Avec React Three Fiber
```tsx
import Ocean from '@/components/Ocean/Ocean'

function Scene() {
  return (
    <>
      <Ocean />
      {/* Autres éléments de la scène */}
    </>
  )
}
```

## Démo

Visitez `/ocean-demo` pour voir une démonstration interactive de l'océan.

## Performance

- **Texture procédurale** : 512x512 pixels pour les normales d'eau
- **Rendu optimisé** : Utilise les shaders GPU natifs
- **Gestion mémoire** : Nettoyage automatique des ressources

## Dépendances

- Three.js
- Water shader (three/examples/jsm/objects/Water.js)
- Sky shader (three/examples/jsm/objects/Sky.js)
- React Three Fiber (pour le composant TSX)

## Notes techniques

- Compatible avec le système de shadows du moteur
- S'intègre avec l'éclairage directionnel existant
- Supporte le fog de la scène
- Texture de normales générée procéduralement si nécessaire
