// Ocean.tsx
import { useRef, useEffect, useState } from 'react';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';
import * as THREE from 'three';
import { useThree, useFrame } from '@react-three/fiber';
import { useTexture } from '@react-three/drei';

export function Ocean() {
  const ref = useRef<THREE.Object3D>();
  const { scene, gl } = useThree();
  const waterNormals = useTexture('textures/waternormals.jpg');
  waterNormals.wrapS = waterNormals.wrapT = THREE.RepeatWrapping;
  
  const sun = new THREE.Vector3();
  
  const [water, setWater] = useState<Water>();
  const [sky, setSky] = useState<Sky>();

  useEffect(() => {
    // Create Water
    const waterGeometry = new THREE.PlaneGeometry(10000, 10000);
    const water = new Water(waterGeometry, {
      textureWidth: 2048,
      textureHeight: 2048,
      waterNormals: waterNormals,
      sunDirection: new THREE.Vector3(),
      sunColor: 0xffffff,
      waterColor: 0x001e0f,
      distortionScale: 1,
      fog: scene.fog !== undefined
    });

    water.rotation.x = -Math.PI / 2;
    water.position.y = -600;
    scene.add(water);
    setWater(water);

    // Create Sky
    const sky = new Sky();
    sky.scale.setScalar(10000);
    scene.add(sky);
    setSky(sky);

    // Sky parameters
    const skyParams = {
      turbidity: 10,
      rayleigh: 2,
      mieCoefficient: 0.005,
      mieDirectionalG: 0.8,
      elevation: 2,
      azimuth: 180
    };

    const pmremGenerator = new THREE.PMREMGenerator(gl);
    let renderTarget: THREE.WebGLRenderTarget | null = null;

    const updateSun = () => {
      const phi = THREE.MathUtils.degToRad(90 - skyParams.elevation);
      const theta = THREE.MathUtils.degToRad(skyParams.azimuth);
      sun.setFromSphericalCoords(1, phi, theta);

      sky.material.uniforms.sunPosition.value.copy(sun);
      water.material.uniforms.sunDirection.value.copy(sun).normalize();

      if (renderTarget) renderTarget.dispose();
      const tempScene = new THREE.Scene();
      tempScene.add(sky);
      renderTarget = pmremGenerator.fromScene(tempScene);
      scene.environment = renderTarget.texture;
    };

    updateSun();

    // Cleanup function
    return () => {
      if (renderTarget) renderTarget.dispose();
      if (water) scene.remove(water);
      if (sky) scene.remove(sky);
    };
  }, [gl, scene, waterNormals]);

  useFrame((_state, delta) => {
    if (water) {
      water.material.uniforms['time'].value += delta;
    }
  });

  return null;
}

export default Ocean;
