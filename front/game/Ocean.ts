import * as THREE from 'three';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';

export class Ocean {
  private water: Water | null = null;
  private sky: Sky | null = null;
  private sun: THREE.Vector3;
  private waterNormals: THREE.Texture | null = null;
  private renderTarget: THREE.WebGLRenderTarget | null = null;
  private pmremGenerator: THREE.PMREMGenerator | null = null;

  constructor(
    private scene: THREE.Scene,
    private renderer: THREE.WebGLRenderer
  ) {
    this.sun = new THREE.Vector3();
    this.pmremGenerator = new THREE.PMREMGenerator(renderer);
    this.loadWaterNormals();
  }

  private async loadWaterNormals(): Promise<void> {
    const textureLoader = new THREE.TextureLoader();
    try {
      this.waterNormals = await new Promise<THREE.Texture>((resolve, reject) => {
        textureLoader.load(
          'textures/waternormals.jpg',
          (texture) => {
            texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
            resolve(texture);
          },
          undefined,
          reject
        );
      });
    } catch (error) {
      console.warn('Could not load water normals texture, creating ocean without it:', error);
      // Create a simple procedural normal texture
      this.createProceduralWaterNormals();
    }
    this.createOcean();
  }

  private createProceduralWaterNormals(): void {
    // Create a simple blue noise texture for water normals
    const size = 512;
    const data = new Uint8Array(size * size * 4);

    for (let i = 0; i < size * size; i++) {
      const x = i % size;
      const y = Math.floor(i / size);
      const noise = Math.sin(x * 0.1) * Math.cos(y * 0.1) * 0.5 + 0.5;

      data[i * 4] = 128 + noise * 127; // R - normal X
      data[i * 4 + 1] = 128 + noise * 127; // G - normal Y
      data[i * 4 + 2] = 255; // B - normal Z (pointing up)
      data[i * 4 + 3] = 255; // A - alpha
    }

    this.waterNormals = new THREE.DataTexture(data, size, size, THREE.RGBAFormat);
    this.waterNormals.wrapS = this.waterNormals.wrapT = THREE.RepeatWrapping;
    this.waterNormals.needsUpdate = true;
  }

  private createOcean(): void {
    this.createWater();
    this.createSky();
    this.updateSun();
  }

  private createWater(): void {
    const waterGeometry = new THREE.PlaneGeometry(10000, 10000);
    
    this.water = new Water(waterGeometry, {
      textureWidth: 2048,
      textureHeight: 2048,
      waterNormals: this.waterNormals || undefined,
      sunDirection: new THREE.Vector3(),
      sunColor: 0xffffff,
      waterColor: 0x001e0f,
      distortionScale: 1,
      fog: this.scene.fog !== undefined
    });

    this.water.rotation.x = -Math.PI / 2;
    this.water.position.y = -13;
    this.scene.add(this.water);
  }

  private createSky(): void {
    this.sky = new Sky();
    this.sky.scale.setScalar(10000);
    this.scene.add(this.sky);
  }

  private updateSun(): void {
    if (!this.sky || !this.water || !this.pmremGenerator) return;

    // Sky parameters
    const skyParams = {
      turbidity: 10,
      rayleigh: 2,
      mieCoefficient: 0.005,
      mieDirectionalG: 0.8,
      elevation: 2,
      azimuth: 180
    };

    const phi = THREE.MathUtils.degToRad(90 - skyParams.elevation);
    const theta = THREE.MathUtils.degToRad(skyParams.azimuth);
    this.sun.setFromSphericalCoords(1, phi, theta);

    this.sky.material.uniforms.sunPosition.value.copy(this.sun);
    this.water.material.uniforms.sunDirection.value.copy(this.sun).normalize();

    if (this.renderTarget) this.renderTarget.dispose();
    this.renderTarget = this.pmremGenerator.fromScene(this.sky);
    this.scene.environment = this.renderTarget.texture;
  }

  public update(deltaTime: number): void {
    if (this.water) {
      this.water.material.uniforms['time'].value += deltaTime / 1000; // Convert to seconds
    }
  }

  public dispose(): void {
    if (this.renderTarget) {
      this.renderTarget.dispose();
      this.renderTarget = null;
    }
    
    if (this.water) {
      this.scene.remove(this.water);
      this.water.geometry.dispose();
      this.water.material.dispose();
      this.water = null;
    }
    
    if (this.sky) {
      this.scene.remove(this.sky);
      this.sky.geometry.dispose();
      this.sky.material.dispose();
      this.sky = null;
    }
    
    if (this.waterNormals) {
      this.waterNormals.dispose();
      this.waterNormals = null;
    }
    
    if (this.pmremGenerator) {
      this.pmremGenerator.dispose();
      this.pmremGenerator = null;
    }
  }

  // Getters for accessing ocean components
  public getWater(): Water | null {
    return this.water;
  }

  public getSky(): Sky | null {
    return this.sky;
  }

  public getSun(): THREE.Vector3 {
    return this.sun;
  }
}
